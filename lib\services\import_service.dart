import 'dart:convert';
import 'dart:io';
import 'package:excel/excel.dart';
import 'package:csv/csv.dart';
import '../models/import_template.dart';
import '../models/movie_model.dart';
import '../models/theater_model.dart';
import '../models/screen_model.dart';
import '../services/firebase_movie_service.dart';
import '../services/theater_service.dart';
import '../services/screen_service.dart';

enum ImportType { movie, theater, screen }

enum FileType { excel, csv, json }

class ImportService {
  final FirebaseMovieService _movieService = FirebaseMovieService();
  final TheaterService _theaterService = TheaterService();
  final ScreenService _screenService = ScreenService();

  // Detect file type from extension
  FileType detectFileType(String filePath) {
    final extension = filePath.toLowerCase().split('.').last;
    switch (extension) {
      case 'xlsx':
      case 'xls':
        return FileType.excel;
      case 'csv':
        return FileType.csv;
      case 'json':
        return FileType.json;
      default:
        throw Exception('Unsupported file type: $extension');
    }
  }

  // Parse file and return raw data
  Future<List<Map<String, dynamic>>> parseFile(File file) async {
    final fileType = detectFileType(file.path);

    switch (fileType) {
      case FileType.excel:
        return _parseExcelFile(file);
      case FileType.csv:
        return _parseCsvFile(file);
      case FileType.json:
        return _parseJsonFile(file);
    }
  }

  // Parse Excel file
  Future<List<Map<String, dynamic>>> _parseExcelFile(File file) async {
    try {
      final bytes = await file.readAsBytes();
      final excel = Excel.decodeBytes(bytes);

      if (excel.tables.isEmpty) {
        throw Exception('Excel file contains no sheets');
      }

      // Use first sheet
      final sheet = excel.tables.values.first;
      if (sheet.rows.isEmpty) {
        throw Exception('Excel sheet is empty');
      }

      // Get headers from first row
      final headerRow = sheet.rows.first;
      final headers =
          headerRow.map((cell) => cell?.value?.toString() ?? '').toList();

      // Remove empty headers
      final validHeaders = <String>[];
      for (int i = 0; i < headers.length; i++) {
        if (headers[i].isNotEmpty) {
          validHeaders.add(headers[i]);
        }
      }

      if (validHeaders.isEmpty) {
        throw Exception('No valid headers found in Excel file');
      }

      // Parse data rows
      final data = <Map<String, dynamic>>[];
      for (int i = 1; i < sheet.rows.length; i++) {
        final row = sheet.rows[i];
        final rowData = <String, dynamic>{};

        for (int j = 0; j < validHeaders.length && j < row.length; j++) {
          final cellValue = row[j]?.value;
          if (cellValue != null) {
            rowData[validHeaders[j]] = _convertCellValue(cellValue);
          }
        }

        // Only add non-empty rows
        if (rowData.isNotEmpty) {
          data.add(rowData);
        }
      }

      return data;
    } catch (e) {
      throw Exception('Failed to parse Excel file: $e');
    }
  }

  // Parse CSV file
  Future<List<Map<String, dynamic>>> _parseCsvFile(File file) async {
    try {
      final content = await file.readAsString();
      final rows = const CsvToListConverter().convert(content);

      if (rows.isEmpty) {
        throw Exception('CSV file is empty');
      }

      // Get headers from first row
      final headers = rows.first.map((cell) => cell.toString()).toList();

      // Parse data rows
      final data = <Map<String, dynamic>>[];
      for (int i = 1; i < rows.length; i++) {
        final row = rows[i];
        final rowData = <String, dynamic>{};

        for (int j = 0; j < headers.length && j < row.length; j++) {
          if (row[j] != null && row[j].toString().isNotEmpty) {
            rowData[headers[j]] = _convertCellValue(row[j]);
          }
        }

        if (rowData.isNotEmpty) {
          data.add(rowData);
        }
      }

      return data;
    } catch (e) {
      throw Exception('Failed to parse CSV file: $e');
    }
  }

  // Parse JSON file
  Future<List<Map<String, dynamic>>> _parseJsonFile(File file) async {
    try {
      final content = await file.readAsString();
      final jsonData = json.decode(content);

      if (jsonData is List) {
        return jsonData.cast<Map<String, dynamic>>();
      } else if (jsonData is Map) {
        return [jsonData.cast<String, dynamic>()];
      } else {
        throw Exception('Invalid JSON format. Expected array or object.');
      }
    } catch (e) {
      throw Exception('Failed to parse JSON file: $e');
    }
  }

  // Convert cell value to appropriate type
  dynamic _convertCellValue(dynamic value) {
    if (value == null) return null;

    final stringValue = value.toString().trim();
    if (stringValue.isEmpty) return null;

    // Try to convert to boolean
    if (stringValue.toLowerCase() == 'true') return true;
    if (stringValue.toLowerCase() == 'false') return false;

    // Try to convert to number
    final intValue = int.tryParse(stringValue);
    if (intValue != null) return intValue;

    final doubleValue = double.tryParse(stringValue);
    if (doubleValue != null) return doubleValue;

    return stringValue;
  }

  // Import movies
  Future<ImportResult<Movie>> importMovies(
      List<Map<String, dynamic>> data) async {
    final successItems = <Movie>[];
    final errors = <ImportError>[];

    for (int i = 0; i < data.length; i++) {
      try {
        final movieData = data[i];
        final movie = _parseMovieData(
            movieData, i + 2); // +2 because row 1 is header, array is 0-indexed

        // Validate required fields
        final validationErrors = _validateMovieData(movieData, i + 2);
        if (validationErrors.isNotEmpty) {
          errors.addAll(validationErrors);
          continue;
        }

        // Add to Firebase
        await _movieService.addMovie(movie);
        successItems.add(movie);
      } catch (e) {
        errors.add(ImportError(
          row: i + 2,
          field: 'general',
          message: 'Failed to import movie: $e',
        ));
      }
    }

    return ImportResult(
      successItems: successItems,
      errors: errors,
      totalProcessed: data.length,
    );
  }

  // Import theaters
  Future<ImportResult<TheaterModel>> importTheaters(
      List<Map<String, dynamic>> data) async {
    final successItems = <TheaterModel>[];
    final errors = <ImportError>[];

    for (int i = 0; i < data.length; i++) {
      try {
        final theaterData = data[i];
        final theater = _parseTheaterData(theaterData, i + 2);

        // Validate required fields
        final validationErrors = _validateTheaterData(theaterData, i + 2);
        if (validationErrors.isNotEmpty) {
          errors.addAll(validationErrors);
          continue;
        }

        // Add to Firebase
        final addedTheater = await _theaterService.addTheater(theater);
        successItems.add(addedTheater);
      } catch (e) {
        errors.add(ImportError(
          row: i + 2,
          field: 'general',
          message: 'Failed to import theater: $e',
        ));
      }
    }

    return ImportResult(
      successItems: successItems,
      errors: errors,
      totalProcessed: data.length,
    );
  }

  // Import screens
  Future<ImportResult<ScreenModel>> importScreens(
      List<Map<String, dynamic>> data) async {
    final successItems = <ScreenModel>[];
    final errors = <ImportError>[];

    for (int i = 0; i < data.length; i++) {
      try {
        final screenData = data[i];
        final screen = _parseScreenData(screenData, i + 2);

        // Validate required fields
        final validationErrors = _validateScreenData(screenData, i + 2);
        if (validationErrors.isNotEmpty) {
          errors.addAll(validationErrors);
          continue;
        }

        // Add to Firebase
        final addedScreen = await _screenService.addScreen(screen);
        successItems.add(addedScreen);
      } catch (e) {
        errors.add(ImportError(
          row: i + 2,
          field: 'general',
          message: 'Failed to import screen: $e',
        ));
      }
    }

    return ImportResult(
      successItems: successItems,
      errors: errors,
      totalProcessed: data.length,
    );
  }

  // Validation methods
  List<ImportError> _validateMovieData(Map<String, dynamic> data, int row) {
    final errors = <ImportError>[];

    // Check required fields
    for (final field in MovieImportTemplate.requiredFields) {
      if (!data.containsKey(field) ||
          data[field] == null ||
          data[field].toString().trim().isEmpty) {
        errors.add(ImportError(
          row: row,
          field: field,
          message: 'Required field is missing or empty',
          value: data[field],
        ));
      }
    }

    // Validate status
    if (data.containsKey('status')) {
      final status = data['status'].toString().toLowerCase();
      if (!['now_playing', 'upcoming', 'ended'].contains(status)) {
        errors.add(ImportError(
          row: row,
          field: 'status',
          message: 'Invalid status. Must be: now_playing, upcoming, or ended',
          value: data['status'],
        ));
      }
    }

    // Validate numeric fields
    if (data.containsKey('runtime') && data['runtime'] != null) {
      final runtime = int.tryParse(data['runtime'].toString());
      if (runtime == null || runtime <= 0) {
        errors.add(ImportError(
          row: row,
          field: 'runtime',
          message: 'Runtime must be a positive number',
          value: data['runtime'],
        ));
      }
    }

    if (data.containsKey('voteAverage') && data['voteAverage'] != null) {
      final voteAverage = double.tryParse(data['voteAverage'].toString());
      if (voteAverage == null || voteAverage < 0 || voteAverage > 10) {
        errors.add(ImportError(
          row: row,
          field: 'voteAverage',
          message: 'Vote average must be between 0 and 10',
          value: data['voteAverage'],
        ));
      }
    }

    return errors;
  }

  List<ImportError> _validateTheaterData(Map<String, dynamic> data, int row) {
    final errors = <ImportError>[];

    // Check required fields
    for (final field in TheaterImportTemplate.requiredFields) {
      if (!data.containsKey(field) ||
          data[field] == null ||
          data[field].toString().trim().isEmpty) {
        errors.add(ImportError(
          row: row,
          field: field,
          message: 'Required field is missing or empty',
          value: data[field],
        ));
      }
    }

    // Validate phone number format
    if (data.containsKey('phoneNumber')) {
      final phone = data['phoneNumber'].toString().trim();
      if (phone.isNotEmpty && !RegExp(r'^[\d\s\-\+\(\)]+$').hasMatch(phone)) {
        errors.add(ImportError(
          row: row,
          field: 'phoneNumber',
          message: 'Invalid phone number format',
          value: data['phoneNumber'],
        ));
      }
    }

    // Validate email format
    if (data.containsKey('email') && data['email'] != null) {
      final email = data['email'].toString().trim();
      if (email.isNotEmpty &&
          !RegExp(r'^[\w\.-]+@[\w\.-]+\.\w+$').hasMatch(email)) {
        errors.add(ImportError(
          row: row,
          field: 'email',
          message: 'Invalid email format',
          value: data['email'],
        ));
      }
    }

    return errors;
  }

  List<ImportError> _validateScreenData(Map<String, dynamic> data, int row) {
    final errors = <ImportError>[];

    // Check required fields
    for (final field in ScreenImportTemplate.requiredFields) {
      if (!data.containsKey(field) ||
          data[field] == null ||
          data[field].toString().trim().isEmpty) {
        errors.add(ImportError(
          row: row,
          field: field,
          message: 'Required field is missing or empty',
          value: data[field],
        ));
      }
    }

    // Validate screen type
    if (data.containsKey('type')) {
      final type = data['type'].toString().toLowerCase();
      if (!['standard', 'premium', 'imax', 'vip', 'dolby'].contains(type)) {
        errors.add(ImportError(
          row: row,
          field: 'type',
          message:
              'Invalid screen type. Must be: standard, premium, imax, vip, or dolby',
          value: data['type'],
        ));
      }
    }

    // Validate numeric fields
    final numericFields = ['totalSeats', 'rows', 'seatsPerRow'];
    for (final field in numericFields) {
      if (data.containsKey(field) && data[field] != null) {
        final value = int.tryParse(data[field].toString());
        if (value == null || value <= 0) {
          errors.add(ImportError(
            row: row,
            field: field,
            message: '$field must be a positive number',
            value: data[field],
          ));
        }
      }
    }

    return errors;
  }

  // Parsing methods
  Movie _parseMovieData(Map<String, dynamic> data, int row) {
    // Parse genres
    List<String> genres = [];
    if (data.containsKey('genres') && data['genres'] != null) {
      final genresStr = data['genres'].toString();
      genres = genresStr
          .split(',')
          .map((g) => g.trim())
          .where((g) => g.isNotEmpty)
          .toList();
    }

    // Parse status
    MovieStatus status = MovieStatus.nowPlaying;
    if (data.containsKey('status')) {
      switch (data['status'].toString().toLowerCase()) {
        case 'upcoming':
          status = MovieStatus.upcoming;
          break;
        case 'ended':
          status = MovieStatus.ended;
          break;
        default:
          status = MovieStatus.nowPlaying;
      }
    }

    // Parse cast if provided
    List<Cast>? cast;
    if (data.containsKey('cast') && data['cast'] != null) {
      try {
        final castData = json.decode(data['cast'].toString());
        if (castData is List) {
          cast = castData.map((c) => Cast.fromJson(c)).toList();
        }
      } catch (e) {
        // Ignore cast parsing errors, will be null
      }
    }

    return Movie(
      id: DateTime.now().millisecondsSinceEpoch + row, // Temporary ID
      title: data['title']?.toString() ?? '',
      originalTitle: data['originalTitle']?.toString(),
      subtitle: data['subtitle']?.toString(),
      overview: data['overview']?.toString(),
      posterPath: data['posterPath']?.toString(),
      backdropPath: data['backdropPath']?.toString(),
      trailerUrl: data['trailerUrl']?.toString(),
      genres: genres,
      releaseDate: data['releaseDate']?.toString(),
      runtime: int.tryParse(data['runtime']?.toString() ?? ''),
      voteAverage: double.tryParse(data['voteAverage']?.toString() ?? ''),
      voteCount: int.tryParse(data['voteCount']?.toString() ?? ''),
      popularity: double.tryParse(data['popularity']?.toString() ?? ''),
      ageRating: data['ageRating']?.toString(),
      language: data['language']?.toString(),
      country: data['country']?.toString(),
      director: data['director']?.toString(),
      cast: cast,
      status: status,
      isActive: data['isActive'] == true ||
          data['isActive']?.toString().toLowerCase() == 'true',
      isHomeBanner: data['isHomeBanner'] == true ||
          data['isHomeBanner']?.toString().toLowerCase() == 'true',
      isSplashBanner: data['isSplashBanner'] == true ||
          data['isSplashBanner']?.toString().toLowerCase() == 'true',
      bannerOrder: int.tryParse(data['bannerOrder']?.toString() ?? ''),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  TheaterModel _parseTheaterData(Map<String, dynamic> data, int row) {
    // Parse facilities
    List<String> facilities = [];
    if (data.containsKey('facilities') && data['facilities'] != null) {
      final facilitiesStr = data['facilities'].toString();
      facilities = facilitiesStr
          .split(',')
          .map((f) => f.trim())
          .where((f) => f.isNotEmpty)
          .toList();
    }

    // Parse operating hours
    Map<String, TheaterOperatingHours> operatingHours = {};
    if (data.containsKey('operatingHours') && data['operatingHours'] != null) {
      try {
        final hoursData = json.decode(data['operatingHours'].toString());
        if (hoursData is Map) {
          hoursData.forEach((day, hours) {
            if (hours is Map &&
                hours.containsKey('open') &&
                hours.containsKey('close')) {
              operatingHours[day] = TheaterOperatingHours(
                open: hours['open'].toString(),
                close: hours['close'].toString(),
              );
            }
          });
        }
      } catch (e) {
        // Use default operating hours if parsing fails
        operatingHours = {
          'monday': TheaterOperatingHours(open: '09:00', close: '23:00'),
          'tuesday': TheaterOperatingHours(open: '09:00', close: '23:00'),
          'wednesday': TheaterOperatingHours(open: '09:00', close: '23:00'),
          'thursday': TheaterOperatingHours(open: '09:00', close: '23:00'),
          'friday': TheaterOperatingHours(open: '09:00', close: '23:00'),
          'saturday': TheaterOperatingHours(open: '09:00', close: '23:00'),
          'sunday': TheaterOperatingHours(open: '09:00', close: '23:00'),
        };
      }
    } else {
      // Default operating hours
      operatingHours = {
        'monday': TheaterOperatingHours(open: '09:00', close: '23:00'),
        'tuesday': TheaterOperatingHours(open: '09:00', close: '23:00'),
        'wednesday': TheaterOperatingHours(open: '09:00', close: '23:00'),
        'thursday': TheaterOperatingHours(open: '09:00', close: '23:00'),
        'friday': TheaterOperatingHours(open: '09:00', close: '23:00'),
        'saturday': TheaterOperatingHours(open: '09:00', close: '23:00'),
        'sunday': TheaterOperatingHours(open: '09:00', close: '23:00'),
      };
    }

    // Create address
    final address = TheaterAddress(
      street: data['address_street']?.toString() ?? '',
      district: data['address_district']?.toString() ?? '',
      city: data['address_city']?.toString() ?? '',
      province: data['address_province']?.toString() ??
          data['address_city']?.toString() ??
          '',
      coordinates: TheaterCoordinates(
        latitude: double.tryParse(data['latitude']?.toString() ?? '') ?? 0.0,
        longitude: double.tryParse(data['longitude']?.toString() ?? '') ?? 0.0,
      ),
    );

    return TheaterModel(
      id: '', // Will be set by Firestore
      name: data['name']?.toString() ?? '',
      address: address,
      phoneNumber: data['phoneNumber']?.toString() ?? '',
      email: data['email']?.toString(),
      facilities: facilities,
      operatingHours: operatingHours,
      isActive: data['isActive'] == true ||
          data['isActive']?.toString().toLowerCase() == 'true',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  ScreenModel _parseScreenData(Map<String, dynamic> data, int row) {
    // Parse amenities
    List<String> amenities = [];
    if (data.containsKey('amenities') && data['amenities'] != null) {
      final amenitiesStr = data['amenities'].toString();
      amenities = amenitiesStr
          .split(',')
          .map((a) => a.trim())
          .where((a) => a.isNotEmpty)
          .toList();
    }

    // Parse screen type
    ScreenType screenType = ScreenType.standard;
    if (data.containsKey('type')) {
      switch (data['type'].toString().toLowerCase()) {
        case 'premium':
          screenType = ScreenType.premium;
          break;
        case 'imax':
          screenType = ScreenType.imax;
          break;
        case 'vip':
          screenType = ScreenType.vip;
          break;
        case 'dolby':
          screenType = ScreenType.dolby;
          break;
        default:
          screenType = ScreenType.standard;
      }
    }

    final totalSeats = int.tryParse(data['totalSeats']?.toString() ?? '') ?? 0;
    final rows = int.tryParse(data['rows']?.toString() ?? '') ?? 0;
    final seatsPerRow =
        int.tryParse(data['seatsPerRow']?.toString() ?? '') ?? 0;

    // Generate basic seat layout
    final seatLayout = <SeatRowModel>[];
    for (int i = 0; i < rows; i++) {
      final rowLabel = String.fromCharCode(65 + i); // A, B, C, etc.
      final seats = <SeatModel>[];

      for (int j = 1; j <= seatsPerRow; j++) {
        seats.add(SeatModel(
          number: '$rowLabel$j',
          type: SeatType.standard,
          isAvailable: true,
        ));
      }

      seatLayout.add(SeatRowModel(
        row: rowLabel,
        seats: seats,
      ));
    }

    return ScreenModel(
      id: '', // Will be set by Firestore
      theaterId: data['theaterId']?.toString() ?? '',
      name: data['name']?.toString() ?? '',
      type: screenType,
      totalSeats: totalSeats,
      rows: rows,
      seatsPerRow: seatsPerRow,
      seatLayout: seatLayout,
      amenities: amenities,
      isActive: data['isActive'] == true ||
          data['isActive']?.toString().toLowerCase() == 'true',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }
}
